from maix import camera, display, image, nn, app, comm, uart, pinmap, time
import struct, os
from struct import pack

report_on = True
APP_CMD_DETECT_RES = 0x02

def print_hex(data : bytes):
    for i in data:
        print(f"0x{i:02X}", end=" ")
    print("")

def encode_objs(objs):
    '''
        encode objs info to bytes body for protocol
        2B x(LE) + 2B y(LE) + 2B w(LE) + 2B h(LE) + 2B idx + 4B score(float) ...
    '''
    body = b''
    for obj in objs:
        body += struct.pack("<hhHHHf", obj.x, obj.y, obj.w, obj.h, obj.class_id, obj.score)
    return body

def send_detection_signal(serial, objs):
    '''Send detection signal via UART when objects are detected'''
    if len(objs) > 0:
        # Use first detected object
        obj = objs[0]
        x = obj.x + obj.w // 2  # Center x coordinate
        y = obj.y + obj.h // 2  # Center y coordinate
        
        # Pack data with protocol format
        data = pack("<HH", x, y)
        data = b"\xAA\xBB\xCC\xDD" + data
        data += pack("B", sum(data) % 255)
        
        serial.write(data)
        print(f"Detection signal sent for {detector.labels[obj.class_id]}:")
        print_hex(data)

# Initialize UART communication
device = "/dev/ttyS0"
serial0 = uart.UART(device, 115200)

# Initialize detection model
model_path = "model_236606.mud"
if not os.path.exists(model_path):
    model_path = "/root/models/maixhub/236606/model_236606.mud"
detector = nn.YOLOv5(model=model_path)

cam = camera.Camera(detector.input_width(), detector.input_height(), detector.input_format())
dis = display.Display()
p = comm.CommProtocol(buff_size = 1024)

while not app.need_exit():
    img = cam.read()
    objs = detector.detect(img, conf_th = 0.5, iou_th = 0.45)

    # Send UART signal when objects detected
    send_detection_signal(serial0, objs)

    if len(objs) > 0 and report_on:
        body = encode_objs(objs)
        p.report(APP_CMD_DETECT_RES, body)

    for obj in objs:
        img.draw_rect(obj.x, obj.y, obj.w, obj.h, color = image.COLOR_RED)
        msg = f'{detector.labels[obj.class_id]}: {obj.score:.2f}'
        img.draw_string(obj.x, obj.y, msg, color = image.COLOR_RED)
    
    dis.show(img)
    
    # Handle incoming UART data
    data = serial0.read()
    if data:
        print("Received UART data:")
        print_hex(data)
        serial0.write(data)  # Echo back received data
    
    time.sleep_ms(1)